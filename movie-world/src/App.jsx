import React, { useState, useEffect } from 'react';
import { Search, Star, Plus, Check, Play, Calendar, Users, Filter, TrendingUp, Heart, X } from 'lucide-react';

const EntertainmentPlatform = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [watchlist, setWatchlist] = useState([]);
  const [watchedList, setWatchedList] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [activeTab, setActiveTab] = useState('discover');
  const [selectedGenre, setSelectedGenre] = useState('all');
  const [contentType, setContentType] = useState('all');
  const [isLoading, setIsLoading] = useState(false);










  // Mock data for demonstration
  const mockTrendingMovies = [
    {
      id: 1,
      title: "Guardians of the Galaxy Vol. 3",
      type: "movie",
      year: 2023,
      genre: ["Action", "Adventure", "Comedy"],
      plot: "Still reeling from the loss of <PERSON><PERSON><PERSON>, <PERSON> rallies his team to defend the universe and protect one of their own.",
      cast: ["<PERSON>", "<PERSON> <PERSON>dana", "Dave Bautista"],
      rating: 7.9,
      imdb: 7.9,
      rottenTomatoes: 82,
      tmdb: 8.1,
      poster: "https://via.placeholder.com/300x450/1a1a2e/16213e?text=GOTG3",
      releaseDate: "2023-05-05"
    },
    {
      id: 2,
      title: "The Bear",
      type: "tv",
      year: 2022,
      genre: ["Comedy", "Drama"],
      plot: "A young chef from the fine dining world returns to Chicago to run his deceased brother's Italian beef sandwich shop.",
      cast: ["Jeremy Allen White", "Ebon Moss-Bachrach", "Ayo Edebiri"],
      rating: 8.7,
      imdb: 8.7,
      rottenTomatoes: 96,
      tmdb: 8.9,
      poster: "https://via.placeholder.com/300x450/2d1b69/1a1a2e?text=THE+BEAR",
      releaseDate: "2022-06-23"
    },
    {
      id: 3,
      title: "Spider-Man: Across the Spider-Verse",
      type: "movie",
      year: 2023,
      genre: ["Animation", "Action", "Adventure"],
      plot: "Miles Morales catapults across the multiverse, where he encounters a team of Spider-People charged with protecting its very existence.",
      cast: ["Shameik Moore", "Hailee Steinfeld", "Brian Tyree Henry"],
      rating: 8.7,
      imdb: 8.7,
      rottenTomatoes: 95,
      tmdb: 8.8,
      poster: "https://via.placeholder.com/300x450/ff6b6b/ffffff?text=SPIDER-VERSE",
      releaseDate: "2023-06-02"
    },
    {
      id: 4,
      title: "Wednesday",
      type: "tv",
      year: 2022,
      genre: ["Comedy", "Crime", "Horror"],
      plot: "Follows Wednesday Addams' years as a student at Nevermore Academy, where she attempts to master her emerging psychic ability.",
      cast: ["Jenna Ortega", "Hunter Doohan", "Emma Myers"],
      rating: 8.1,
      imdb: 8.1,
      rottenTomatoes: 78,
      tmdb: 8.4,
      poster: "https://via.placeholder.com/300x450/0f3460/ffffff?text=WEDNESDAY",
      releaseDate: "2022-11-23"
    },
    {
      id: 5,
      title: "Oppenheimer",
      type: "movie",
      year: 2023,
      genre: ["Biography", "Drama", "History"],
      plot: "The story of American scientist J. Robert Oppenheimer and his role in the development of the atomic bomb.",
      cast: ["Cillian Murphy", "Emily Blunt", "Matt Damon"],
      rating: 8.3,
      imdb: 8.3,
      rottenTomatoes: 93,
      tmdb: 8.2,
      poster: "https://via.placeholder.com/300x450/d63031/ffffff?text=OPPENHEIMER",
      releaseDate: "2023-07-21"
    },
    {
      id: 6,
      title: "House of the Dragon",
      type: "tv",
      year: 2022,
      genre: ["Action", "Adventure", "Drama"],
      plot: "An internal succession war within House Targaryen at the height of its power, 200 years before the events of Game of Thrones.",
      cast: ["Paddy Considine", "Emma D'Arcy", "Matt Smith"],
      rating: 8.4,
      imdb: 8.4,
      rottenTomatoes: 84,
      tmdb: 8.6,
      poster: "https://via.placeholder.com/300x450/74b9ff/ffffff?text=HOTD",
      releaseDate: "2022-08-21"
    }
  ];

  const genres = ["All", "Action", "Adventure", "Animation", "Biography", "Comedy", "Crime", "Drama", "History", "Horror"];

  useEffect(() => {
    if (searchQuery.length > 0) {
      setIsLoading(true);
      // Simulate API call delay
      const timer = setTimeout(() => {
        const filtered = mockTrendingMovies.filter(item =>
          item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.cast.some(actor => actor.toLowerCase().includes(searchQuery.toLowerCase())) ||
          item.genre.some(g => g.toLowerCase().includes(searchQuery.toLowerCase()))
        );
        setSearchResults(filtered);
        setIsLoading(false);
      }, 300);
      return () => clearTimeout(timer);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery]);

  const filteredContent = mockTrendingMovies.filter(item => {
    const genreMatch = selectedGenre === 'all' || item.genre.some(g => g.toLowerCase() === selectedGenre.toLowerCase());
    const typeMatch = contentType === 'all' || item.type === contentType;
    return genreMatch && typeMatch;
  });

  const addToWatchlist = (item) => {
    if (!watchlist.find(w => w.id === item.id)) {
      setWatchlist([...watchlist, item]);
    }
  };

  const removeFromWatchlist = (id) => {
    setWatchlist(watchlist.filter(item => item.id !== id));
  };

  const markAsWatched = (item) => {
    if (!watchedList.find(w => w.id === item.id)) {
      setWatchedList([...watchedList, item]);
      removeFromWatchlist(item.id);
    }
  };

  const getRecommendations = () => {
    if (watchlist.length === 0) return [];
    
    const watchlistGenres = watchlist.flatMap(item => item.genre);
    const genreCounts = watchlistGenres.reduce((acc, genre) => {
      acc[genre] = (acc[genre] || 0) + 1;
      return acc;
    }, {});
    
    const topGenres = Object.entries(genreCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([genre]) => genre);

    return mockTrendingMovies.filter(item => 
      !watchlist.find(w => w.id === item.id) &&
      !watchedList.find(w => w.id === item.id) &&
      item.genre.some(g => topGenres.includes(g))
    ).slice(0, 4);
  };

  const ContentCard = ({ item, showActions = true }) => (
    <div className="bg-gray-800 rounded-lg overflow-hidden hover:transform hover:scale-105 transition-all duration-300 shadow-lg">
      <div className="relative">
        <img 
          src={item.poster} 
          alt={item.title}
          className="w-full h-64 object-cover"
        />
        <div className="absolute top-2 right-2">
          <span className={`px-2 py-1 rounded text-xs font-bold ${
            item.type === 'movie' ? 'bg-blue-600' : 'bg-purple-600'
          } text-white`}>
            {item.type === 'movie' ? 'Movie' : 'TV Show'}
          </span>
        </div>
        <div className="absolute bottom-2 left-2 bg-black bg-opacity-70 px-2 py-1 rounded flex items-center">
          <Star className="w-4 h-4 text-yellow-400 mr-1" />
          <span className="text-white text-sm">{item.rating}</span>
        </div>
      </div>
      <div className="p-4">
        <h3 className="text-white font-bold text-lg mb-2 line-clamp-1">{item.title}</h3>
        <p className="text-gray-400 text-sm mb-2">{item.year} • {item.genre.join(', ')}</p>
        <p className="text-gray-300 text-sm mb-4 line-clamp-2">{item.plot}</p>
        
        {showActions && (
          <div className="flex gap-2">
            <button
              onClick={() => setSelectedItem(item)}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium flex items-center justify-center gap-2 transition-colors"
            >
              <Play className="w-4 h-4" />
              Details
            </button>
            
            {!watchlist.find(w => w.id === item.id) && !watchedList.find(w => w.id === item.id) ? (
              <button
                onClick={() => addToWatchlist(item)}
                className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm font-medium flex items-center justify-center transition-colors"
              >
                <Plus className="w-4 h-4" />
              </button>
            ) : watchedList.find(w => w.id === item.id) ? (
              <button className="bg-green-600 text-white px-3 py-2 rounded text-sm font-medium flex items-center justify-center">
                <Check className="w-4 h-4" />
              </button>
            ) : (
              <button
                onClick={() => markAsWatched(item)}
                className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 rounded text-sm font-medium flex items-center justify-center transition-colors"
              >
                <Check className="w-4 h-4" />
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );

  const DetailModal = ({ item, onClose }) => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
        <div className="relative">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
          >
            <X className="w-6 h-6" />
          </button>
          
          <div className="flex flex-col md:flex-row">
            <img 
              src={item.poster} 
              alt={item.title}
              className="w-full md:w-80 h-96 md:h-auto object-cover"
            />
            
            <div className="p-6 flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h2 className="text-white text-3xl font-bold">{item.title}</h2>
                <span className={`px-2 py-1 rounded text-sm font-bold ${
                  item.type === 'movie' ? 'bg-blue-600' : 'bg-purple-600'
                } text-white`}>
                  {item.type === 'movie' ? 'Movie' : 'TV Show'}
                </span>
              </div>
              
              <div className="flex items-center gap-4 mb-4 text-gray-400">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{item.year}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span>{item.genre.join(', ')}</span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-white text-lg font-semibold mb-2">Ratings</h3>
                <div className="flex gap-4">
                  <div className="text-center">
                    <div className="text-yellow-400 font-bold">{item.imdb}</div>
                    <div className="text-gray-400 text-sm">IMDB</div>
                  </div>
                  <div className="text-center">
                    <div className="text-red-400 font-bold">{item.rottenTomatoes}%</div>
                    <div className="text-gray-400 text-sm">RT</div>
                  </div>
                  <div className="text-center">
                    <div className="text-blue-400 font-bold">{item.tmdb}</div>
                    <div className="text-gray-400 text-sm">TMDB</div>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-white text-lg font-semibold mb-2">Plot</h3>
                <p className="text-gray-300">{item.plot}</p>
              </div>

              <div className="mb-6">
                <h3 className="text-white text-lg font-semibold mb-2">Cast</h3>
                <p className="text-gray-300">{item.cast.join(', ')}</p>
              </div>

              <div className="flex gap-3">
                {!watchlist.find(w => w.id === item.id) && !watchedList.find(w => w.id === item.id) && (
                  <button
                    onClick={() => {
                      addToWatchlist(item);
                      onClose();
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded font-medium flex items-center gap-2 transition-colors"
                  >
                    <Plus className="w-5 h-5" />
                    Add to Watchlist
                  </button>
                )}
                
                {watchlist.find(w => w.id === item.id) && (
                  <button
                    onClick={() => {
                      markAsWatched(item);
                      onClose();
                    }}
                    className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded font-medium flex items-center gap-2 transition-colors"
                  >
                    <Check className="w-5 h-5" />
                    Mark as Watched
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 shadow-lg sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-white">CinemaHub</h1>
            
            <div className="flex-1 max-w-2xl mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search movies, TV shows, actors..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <nav className="flex gap-6">
              {['discover', 'watchlist', 'watched'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`px-4 py-2 rounded-lg font-medium capitalize transition-colors ${
                    activeTab === tab
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                  }`}
                >
                  {tab}
                  {tab === 'watchlist' && watchlist.length > 0 && (
                    <span className="ml-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                      {watchlist.length}
                    </span>
                  )}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 py-8">
        {/* Search Results */}
        {searchQuery && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-white mb-6">
              Search Results for "{searchQuery}"
            </h2>
            {isLoading ? (
              <div className="text-center py-8">
                <div className="text-gray-400">Searching...</div>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {searchResults.map((item) => (
                  <ContentCard key={item.id} item={item} />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-400">No results found</div>
              </div>
            )}
          </section>
        )}

        {/* Main Content */}
        {!searchQuery && (
          <>
            {activeTab === 'discover' && (
              <>
                {/* Filters */}
                <section className="mb-8">
                  <div className="flex flex-wrap gap-4 items-center">
                    <div className="flex items-center gap-2">
                      <Filter className="w-5 h-5 text-gray-400" />
                      <span className="text-white font-medium">Filters:</span>
                    </div>
                    
                    <select
                      value={selectedGenre}
                      onChange={(e) => setSelectedGenre(e.target.value)}
                      className="bg-gray-700 text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {genres.map((genre) => (
                        <option key={genre} value={genre.toLowerCase()}>
                          {genre}
                        </option>
                      ))}
                    </select>

                    <select
                      value={contentType}
                      onChange={(e) => setContentType(e.target.value)}
                      className="bg-gray-700 text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">All Types</option>
                      <option value="movie">Movies</option>
                      <option value="tv">TV Shows</option>
                    </select>
                  </div>
                </section>

                {/* Trending Content */}
                <section className="mb-12">
                  <div className="flex items-center gap-2 mb-6">
                    <TrendingUp className="w-6 h-6 text-orange-500" />
                    <h2 className="text-2xl font-bold text-white">Trending Now</h2>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {filteredContent.map((item) => (
                      <ContentCard key={item.id} item={item} />
                    ))}
                  </div>
                </section>

                {/* Recommendations */}
                {getRecommendations().length > 0 && (
                  <section className="mb-12">
                    <div className="flex items-center gap-2 mb-6">
                      <Heart className="w-6 h-6 text-red-500" />
                      <h2 className="text-2xl font-bold text-white">Recommended for You</h2>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                      {getRecommendations().map((item) => (
                        <ContentCard key={item.id} item={item} />
                      ))}
                    </div>
                  </section>
                )}
              </>
            )}

            {activeTab === 'watchlist' && (
              <section>
                <h2 className="text-2xl font-bold text-white mb-6">My Watchlist</h2>
                {watchlist.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {watchlist.map((item) => (
                      <div key={item.id} className="relative">
                        <ContentCard item={item} />
                        <button
                          onClick={() => removeFromWatchlist(item.id)}
                          className="absolute top-2 left-2 bg-red-600 hover:bg-red-700 text-white p-1 rounded-full transition-colors"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">Your watchlist is empty</div>
                    <button
                      onClick={() => setActiveTab('discover')}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                    >
                      Discover Content
                    </button>
                  </div>
                )}
              </section>
            )}

            {activeTab === 'watched' && (
              <section>
                <h2 className="text-2xl font-bold text-white mb-6">Watched</h2>
                {watchedList.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {watchedList.map((item) => (
                      <ContentCard key={item.id} item={item} showActions={false} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">No watched content yet</div>
                    <button
                      onClick={() => setActiveTab('discover')}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                    >
                      Start Watching
                    </button>
                  </div>
                )}
              </section>
            )}
          </>
        )}
      </main>

      {/* Detail Modal */}
      {selectedItem && (
        <DetailModal
          item={selectedItem}
          onClose={() => setSelectedItem(null)}
        />
      )}
    </div>
  );
};

export default EntertainmentPlatform;